{"name": "@mui/system", "version": "5.18.0", "private": false, "author": "MUI Team", "description": "MUI System is a set of CSS utilities to help you build custom designs more efficiently. It makes it possible to rapidly lay out custom designs.", "main": "./index.js", "keywords": ["react", "react-component", "mui", "system"], "repository": {"type": "git", "url": "https://github.com/mui/material-ui.git", "directory": "packages/mui-system"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "https://v5.mui.com/system/getting-started/", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "dependencies": {"@babel/runtime": "^7.23.9", "clsx": "^2.1.0", "csstype": "^3.1.3", "prop-types": "^15.8.1", "@mui/styled-engine": "^5.18.0", "@mui/types": "~7.2.15", "@mui/utils": "^5.17.1", "@mui/private-theming": "^5.17.1"}, "peerDependencies": {"@emotion/react": "^11.5.0", "@emotion/styled": "^11.3.0", "@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}}, "sideEffects": false, "publishConfig": {"access": "public", "directory": "build"}, "engines": {"node": ">=12.0.0"}, "module": "./esm/index.js", "types": "./index.d.ts"}