from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, Text, ForeignKey, LargeBinary
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base

class User(Base):
    __tablename__ = "users"
    
    user_id = Column(Integer, primary_key=True, index=True)
    first_name = Column(String(100), nullable=False)
    middle_names = Column(String(200), nullable=True)
    last_name = Column(String(100), nullable=True)
    date_of_birth = Column(DateTime, nullable=True)
    department = Column(String(100), nullable=True)
    role = Column(String(100), nullable=True)
    contact_info = Column(String(200), nullable=True)
    reference_photo_path = Column(String(500), nullable=True)
    face_encoding = Column(LargeBinary, nullable=True)  # Store face encoding as binary
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    is_active = Column(Boolean, default=True)
    
    # Relationship with attendance records
    attendance_records = relationship("Attendance", back_populates="user")

class Attendance(Base):
    __tablename__ = "attendance"
    
    attendance_id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.user_id"), nullable=False)
    camera_id = Column(String(100), nullable=False)
    detected_at = Column(DateTime(timezone=True), server_default=func.now())
    confidence_score = Column(Float, nullable=False)
    detection_photo_path = Column(String(500), nullable=True)
    location = Column(String(200), nullable=True)
    
    # Relationship with user
    user = relationship("User", back_populates="attendance_records")

class Camera(Base):
    __tablename__ = "cameras"
    
    camera_id = Column(String(100), primary_key=True)
    name = Column(String(200), nullable=False)
    camera_type = Column(String(50), nullable=False)  # 'local' or 'rtsp'
    connection_string = Column(String(500), nullable=False)
    location = Column(String(200), nullable=True)
    is_active = Column(Boolean, default=True)
    resolution_width = Column(Integer, default=640)
    resolution_height = Column(Integer, default=480)
    frame_rate = Column(Integer, default=30)
    username = Column(String(100), nullable=True)  # For RTSP cameras
    password = Column(String(100), nullable=True)  # For RTSP cameras
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class SystemSettings(Base):
    __tablename__ = "system_settings"
    
    setting_id = Column(Integer, primary_key=True, index=True)
    setting_key = Column(String(100), unique=True, nullable=False)
    setting_value = Column(Text, nullable=False)
    description = Column(Text, nullable=True)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
