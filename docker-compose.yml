version: '3.8'

services:
  # PostgreSQL Database (for production)
  postgres:
    image: postgres:16
    environment:
      POSTGRES_DB: face_recognition
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5434:5432"
    networks:
      - face_recognition_network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=***********************************************/face_recognition
      - SECRET_KEY=production-secret-key-change-this
    volumes:
      - ./uploads:/app/uploads
    ports:
      - "8001:8000"
    depends_on:
      - postgres
    networks:
      - face_recognition_network

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - face_recognition_network

  # Nginx Reverse Proxy (optional) - commented out until nginx.conf is created
  # nginx:
  #   image: nginx:alpine
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf
  #   ports:
  #     - "80:80"
  #   depends_on:
  #     - backend
  #     - frontend
  #   networks:
  #     - face_recognition_network

volumes:
  postgres_data:

networks:
  face_recognition_network:
    driver: bridge
