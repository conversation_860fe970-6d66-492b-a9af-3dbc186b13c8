from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import Static<PERSON>iles
import os
from pathlib import Path

from app.core.config import settings
from app.db.database import engine, Base
from app.api import users, attendance, cameras, recognition

# Create database tables
Base.metadata.create_all(bind=engine)

# Create FastAPI app
app = FastAPI(
    title="Face Recognition Attendance System",
    description="A real-time attendance monitoring system using face recognition",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create uploads directory if it doesn't exist
uploads_dir = Path(settings.UPLOAD_DIR)
uploads_dir.mkdir(exist_ok=True)

# Mount static files for uploaded images
app.mount("/uploads", StaticFiles(directory=str(uploads_dir)), name="uploads")

# Include API routes
app.include_router(users.router, prefix="/api/users", tags=["users"])
app.include_router(attendance.router, prefix="/api/attendance", tags=["attendance"])
app.include_router(cameras.router, prefix="/api/cameras", tags=["cameras"])
app.include_router(recognition.router, prefix="/api/recognition", tags=["recognition"])

@app.get("/")
async def root():
    return {"message": "Face Recognition Attendance System API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
