from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any

from app.db.database import get_db
from app.db.models import Camera
from app.db.schemas import Camera as CameraSchema, CameraCreate, CameraUpdate
from app.services.camera_service import camera_manager

router = APIRouter()

@router.post("/", response_model=CameraSchema)
def create_camera(camera: CameraCreate, db: Session = Depends(get_db)):
    """Create a new camera configuration."""
    
    # Check if camera ID already exists
    existing_camera = db.query(Camera).filter(Camera.camera_id == camera.camera_id).first()
    if existing_camera:
        raise HTTPException(status_code=409, detail="Camera ID already exists")
    
    # Create camera in database
    db_camera = Camera(**camera.dict())
    db.add(db_camera)
    db.commit()
    db.refresh(db_camera)
    
    # Add camera to camera manager
    camera_manager.add_camera(
        camera_id=db_camera.camera_id,
        connection_string=db_camera.connection_string,
        camera_type=db_camera.camera_type
    )
    
    return db_camera

@router.get("/", response_model=List[CameraSchema])
def get_cameras(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    is_active: Optional[bool] = Query(None),
    camera_type: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """Get list of cameras with optional filtering."""
    query = db.query(Camera)
    
    if is_active is not None:
        query = query.filter(Camera.is_active == is_active)
    
    if camera_type:
        query = query.filter(Camera.camera_type == camera_type)
    
    cameras = query.offset(skip).limit(limit).all()
    return cameras

@router.get("/{camera_id}", response_model=CameraSchema)
def get_camera(camera_id: str, db: Session = Depends(get_db)):
    """Get a specific camera by ID."""
    camera = db.query(Camera).filter(Camera.camera_id == camera_id).first()
    if not camera:
        raise HTTPException(status_code=404, detail="Camera not found")
    return camera

@router.put("/{camera_id}", response_model=CameraSchema)
def update_camera(
    camera_id: str,
    camera_update: CameraUpdate,
    db: Session = Depends(get_db)
):
    """Update a camera configuration."""
    
    # Get existing camera
    db_camera = db.query(Camera).filter(Camera.camera_id == camera_id).first()
    if not db_camera:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    # Update fields
    update_data = camera_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_camera, field, value)
    
    db.commit()
    db.refresh(db_camera)
    
    # Update camera manager if connection details changed
    if any(field in update_data for field in ['connection_string', 'camera_type', 'is_active']):
        # Remove and re-add camera to camera manager
        camera_manager.remove_camera(camera_id)
        if db_camera.is_active:
            camera_manager.add_camera(
                camera_id=db_camera.camera_id,
                connection_string=db_camera.connection_string,
                camera_type=db_camera.camera_type
            )
    
    return db_camera

@router.delete("/{camera_id}")
def delete_camera(camera_id: str, db: Session = Depends(get_db)):
    """Delete a camera."""
    db_camera = db.query(Camera).filter(Camera.camera_id == camera_id).first()
    if not db_camera:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    # Remove from camera manager
    camera_manager.remove_camera(camera_id)
    
    # Delete from database
    db.delete(db_camera)
    db.commit()
    
    return {"message": "Camera deleted successfully"}

@router.post("/{camera_id}/start")
def start_camera(camera_id: str, db: Session = Depends(get_db)):
    """Start streaming for a specific camera."""
    
    # Check if camera exists in database
    camera = db.query(Camera).filter(Camera.camera_id == camera_id).first()
    if not camera:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    if not camera.is_active:
        raise HTTPException(status_code=400, detail="Camera is not active")
    
    # Add to camera manager if not already added
    if camera_id not in camera_manager.cameras:
        camera_manager.add_camera(
            camera_id=camera.camera_id,
            connection_string=camera.connection_string,
            camera_type=camera.camera_type
        )
    
    # Start streaming
    success = camera_manager.start_camera(camera_id)
    if not success:
        raise HTTPException(status_code=400, detail="Failed to start camera")
    
    return {"message": f"Camera {camera_id} started successfully"}

@router.post("/{camera_id}/stop")
def stop_camera(camera_id: str):
    """Stop streaming for a specific camera."""
    success = camera_manager.stop_camera(camera_id)
    if not success:
        raise HTTPException(status_code=404, detail="Camera not found in manager")
    
    return {"message": f"Camera {camera_id} stopped successfully"}

@router.get("/{camera_id}/status")
def get_camera_status(camera_id: str):
    """Get the current status of a specific camera."""
    status = camera_manager.get_camera_status(camera_id)
    if status is None:
        raise HTTPException(status_code=404, detail="Camera not found in manager")
    
    return {
        "camera_id": camera_id,
        "is_connected": status.is_connected,
        "last_frame_time": status.last_frame_time,
        "error_message": status.error_message,
        "frame_count": status.frame_count,
        "fps": status.fps
    }

@router.get("/status/all")
def get_all_camera_statuses():
    """Get the status of all cameras."""
    statuses = camera_manager.get_all_camera_statuses()
    
    result = {}
    for camera_id, status in statuses.items():
        result[camera_id] = {
            "is_connected": status.is_connected,
            "last_frame_time": status.last_frame_time,
            "error_message": status.error_message,
            "frame_count": status.frame_count,
            "fps": status.fps
        }
    
    return result

@router.post("/start-all")
def start_all_cameras():
    """Start streaming for all active cameras."""
    camera_manager.start_all_cameras()
    return {"message": "All cameras started"}

@router.post("/stop-all")
def stop_all_cameras():
    """Stop streaming for all cameras."""
    camera_manager.stop_all_cameras()
    return {"message": "All cameras stopped"}

@router.post("/{camera_id}/test-connection")
def test_camera_connection(camera_id: str, db: Session = Depends(get_db)):
    """Test connection to a specific camera."""
    
    # Get camera from database
    camera = db.query(Camera).filter(Camera.camera_id == camera_id).first()
    if not camera:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    # Create temporary camera stream to test connection
    from app.services.camera_service import CameraStream
    
    test_stream = CameraStream(
        camera_id=f"test_{camera_id}",
        connection_string=camera.connection_string,
        camera_type=camera.camera_type
    )
    
    success = test_stream.connect()
    
    if success:
        test_stream.disconnect()
        return {"message": "Connection successful", "status": "connected"}
    else:
        return {
            "message": "Connection failed",
            "status": "failed",
            "error": test_stream.status.error_message
        }
