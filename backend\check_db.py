import sqlite3
import os

db_path = 'face_recognition.db'

if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    tables = [row[0] for row in conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()]
    print("Database exists.")
    print("Tables:", tables)
    
    # Check if we have sample data
    if 'users' in tables:
        user_count = conn.execute("SELECT COUNT(*) FROM users").fetchone()[0]
        print(f"Users count: {user_count}")
    
    if 'cameras' in tables:
        camera_count = conn.execute("SELECT COUNT(*) FROM cameras").fetchone()[0]
        print(f"Cameras count: {camera_count}")
        
    conn.close()
else:
    print("Database does not exist. Need to initialize.")
