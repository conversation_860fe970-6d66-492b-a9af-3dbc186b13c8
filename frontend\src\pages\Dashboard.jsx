import React, { useState, useEffect } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Alert,
  CircularProgress,
  Paper,
  Divider,
} from '@mui/material'
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  People as PeopleIcon,
  EventNote as EventNoteIcon,
  Videocam as VideocamIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material'
import { format } from 'date-fns'
import { useWebSocket } from '../contexts/WebSocketContext'
import { attendanceAPI, recognitionAPI, cameraAPI } from '../services/api'

const Dashboard = () => {
  const [stats, setStats] = useState({
    total_today: 0,
    unique_users_today: 0,
    total_this_week: 0,
    total_this_month: 0,
  })
  const [recentAttendance, setRecentAttendance] = useState([])
  const [recognitionStatus, setRecognitionStatus] = useState({
    is_running: false,
    loaded_faces: 0,
    active_cameras: 0,
    camera_statuses: {},
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const { attendanceUpdates, isConnected } = useWebSocket()

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)

      const [statsResponse, recentResponse, statusResponse] = await Promise.all([
        attendanceAPI.getAttendanceStats(),
        attendanceAPI.getRecentAttendance(10),
        recognitionAPI.getRecognitionStatus(),
      ])

      setStats(statsResponse.data)
      setRecentAttendance(recentResponse.data)
      setRecognitionStatus(statusResponse.data)
    } catch (err) {
      console.error('Error fetching dashboard data:', err)
      setError('Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
    const interval = setInterval(fetchData, 30000) // Refresh every 30 seconds
    return () => clearInterval(interval)
  }, [])

  // Update recent attendance when new attendance is detected via WebSocket
  useEffect(() => {
    if (attendanceUpdates.length > 0) {
      const latestUpdate = attendanceUpdates[0]
      setRecentAttendance(prev => [
        {
          attendance_id: latestUpdate.attendance_id,
          user_id: latestUpdate.user_id,
          user: { first_name: latestUpdate.user_name.split(' ')[0], last_name: latestUpdate.user_name.split(' ').slice(1).join(' ') },
          camera_id: latestUpdate.camera_id,
          detected_at: latestUpdate.detected_at,
          confidence_score: latestUpdate.confidence_score,
        },
        ...prev.slice(0, 9) // Keep only 10 items
      ])

      // Update stats
      setStats(prev => ({
        ...prev,
        total_today: prev.total_today + 1,
      }))
    }
  }, [attendanceUpdates])

  const handleStartRecognition = async () => {
    try {
      await recognitionAPI.startRecognition()
      await fetchData() // Refresh status
    } catch (err) {
      console.error('Error starting recognition:', err)
      setError('Failed to start recognition')
    }
  }

  const handleStopRecognition = async () => {
    try {
      await recognitionAPI.stopRecognition()
      await fetchData() // Refresh status
    } catch (err) {
      console.error('Error stopping recognition:', err)
      setError('Failed to stop recognition')
    }
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Dashboard
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchData}
          >
            Refresh
          </Button>
          {recognitionStatus.is_running ? (
            <Button
              variant="contained"
              color="error"
              startIcon={<StopIcon />}
              onClick={handleStopRecognition}
            >
              Stop Recognition
            </Button>
          ) : (
            <Button
              variant="contained"
              color="primary"
              startIcon={<PlayIcon />}
              onClick={handleStartRecognition}
            >
              Start Recognition
            </Button>
          )}
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* System Status */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            System Status
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body2" color="text.secondary">
                  Recognition:
                </Typography>
                <Chip
                  label={recognitionStatus.is_running ? 'Running' : 'Stopped'}
                  color={recognitionStatus.is_running ? 'success' : 'default'}
                  size="small"
                />
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body2" color="text.secondary">
                  WebSocket:
                </Typography>
                <Chip
                  label={isConnected ? 'Connected' : 'Disconnected'}
                  color={isConnected ? 'success' : 'error'}
                  size="small"
                />
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="text.secondary">
                Loaded Faces: {recognitionStatus.loaded_faces}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="text.secondary">
                Active Cameras: {recognitionStatus.active_cameras}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <EventNoteIcon color="primary" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" component="div">
                    {stats.total_today}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Today's Attendance
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <PeopleIcon color="secondary" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" component="div">
                    {stats.unique_users_today}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Unique Users Today
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <EventNoteIcon color="success" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" component="div">
                    {stats.total_this_week}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    This Week
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <EventNoteIcon color="warning" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" component="div">
                    {stats.total_this_month}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    This Month
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Attendance */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Recent Attendance
          </Typography>
          {recentAttendance.length === 0 ? (
            <Typography variant="body2" color="text.secondary" sx={{ py: 2 }}>
              No recent attendance records
            </Typography>
          ) : (
            <List>
              {recentAttendance.map((record, index) => (
                <React.Fragment key={record.attendance_id}>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        {record.user?.first_name?.charAt(0) || 'U'}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={`${record.user?.first_name || 'Unknown'} ${record.user?.last_name || ''}`}
                      secondary={
                        <Box>
                          <Typography variant="body2" component="span">
                            Camera: {record.camera_id} •
                            Confidence: {(record.confidence_score * 100).toFixed(1)}%
                          </Typography>
                          <br />
                          <Typography variant="caption" color="text.secondary">
                            {format(new Date(record.detected_at), 'MMM dd, yyyy HH:mm:ss')}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < recentAttendance.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          )}
        </CardContent>
      </Card>
    </Box>
  )
}

export default Dashboard
