import React, { createContext, useContext, useEffect, useState, useRef } from 'react'
import { io } from 'socket.io-client'

const WebSocketContext = createContext()

export const useWebSocket = () => {
  const context = useContext(WebSocketContext)
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider')
  }
  return context
}

export const WebSocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null)
  const [isConnected, setIsConnected] = useState(false)
  const [lastMessage, setLastMessage] = useState(null)
  const [attendanceUpdates, setAttendanceUpdates] = useState([])
  const reconnectTimeoutRef = useRef(null)
  const maxReconnectAttempts = 5
  const reconnectAttemptRef = useRef(0)

  const connectWebSocket = () => {
    try {
      // Use WebSocket directly for the recognition endpoint
      const wsUrl = `ws://${window.location.host}/api/recognition/ws`
      const ws = new WebSocket(wsUrl)

      ws.onopen = () => {
        console.log('WebSocket connected')
        setIsConnected(true)
        reconnectAttemptRef.current = 0
        
        // Send ping to keep connection alive
        const pingInterval = setInterval(() => {
          if (ws.readyState === WebSocket.OPEN) {
            ws.send('ping')
          } else {
            clearInterval(pingInterval)
          }
        }, 30000)
      }

      ws.onmessage = (event) => {
        try {
          if (event.data === 'pong') {
            return // Ignore pong responses
          }

          const message = JSON.parse(event.data)
          setLastMessage(message)

          // Handle different message types
          if (message.type === 'attendance_detected') {
            setAttendanceUpdates(prev => [message.data, ...prev.slice(0, 49)]) // Keep last 50
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }

      ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason)
        setIsConnected(false)
        setSocket(null)

        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectAttemptRef.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttemptRef.current), 30000)
          console.log(`Attempting to reconnect in ${delay}ms...`)
          
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttemptRef.current += 1
            connectWebSocket()
          }, delay)
        }
      }

      ws.onerror = (error) => {
        console.error('WebSocket error:', error)
      }

      setSocket(ws)
    } catch (error) {
      console.error('Error creating WebSocket connection:', error)
    }
  }

  useEffect(() => {
    connectWebSocket()

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
      if (socket) {
        socket.close(1000, 'Component unmounting')
      }
    }
  }, [])

  const sendMessage = (message) => {
    if (socket && socket.readyState === WebSocket.OPEN) {
      socket.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket is not connected')
    }
  }

  const clearAttendanceUpdates = () => {
    setAttendanceUpdates([])
  }

  const value = {
    socket,
    isConnected,
    lastMessage,
    attendanceUpdates,
    sendMessage,
    clearAttendanceUpdates,
    reconnect: connectWebSocket,
  }

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  )
}
