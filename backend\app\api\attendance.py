from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from typing import List, Optional
from datetime import datetime, timedelta, date

from app.db.database import get_db
from app.db.models import Attendance, User
from app.db.schemas import Attendance as AttendanceSchema, AttendanceCreate, AttendanceStats
from app.core.config import settings

router = APIRouter()

@router.post("/", response_model=AttendanceSchema)
def create_attendance(
    attendance: AttendanceCreate,
    db: Session = Depends(get_db)
):
    """Create a new attendance record."""
    
    # Check if user exists
    user = db.query(User).filter(User.user_id == attendance.user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Check for duplicate attendance within threshold
    threshold_time = datetime.utcnow() - timedelta(minutes=settings.DUPLICATE_ATTENDANCE_THRESHOLD_MINUTES)
    
    existing_attendance = db.query(Attendance).filter(
        and_(
            Attendance.user_id == attendance.user_id,
            Attendance.camera_id == attendance.camera_id,
            Attendance.detected_at >= threshold_time
        )
    ).first()
    
    if existing_attendance:
        raise HTTPException(
            status_code=409,
            detail=f"Duplicate attendance detected. User was already recorded within the last {settings.DUPLICATE_ATTENDANCE_THRESHOLD_MINUTES} minutes."
        )
    
    # Create attendance record
    db_attendance = Attendance(**attendance.dict())
    db.add(db_attendance)
    db.commit()
    db.refresh(db_attendance)
    
    return db_attendance

@router.get("/", response_model=List[AttendanceSchema])
def get_attendance_records(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    user_id: Optional[int] = Query(None),
    camera_id: Optional[str] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db)
):
    """Get attendance records with optional filtering."""
    query = db.query(Attendance).join(User)
    
    if user_id:
        query = query.filter(Attendance.user_id == user_id)
    
    if camera_id:
        query = query.filter(Attendance.camera_id == camera_id)
    
    if start_date:
        query = query.filter(Attendance.detected_at >= start_date)
    
    if end_date:
        # Add one day to include the entire end date
        end_datetime = datetime.combine(end_date, datetime.min.time()) + timedelta(days=1)
        query = query.filter(Attendance.detected_at < end_datetime)
    
    # Order by most recent first
    query = query.order_by(Attendance.detected_at.desc())
    
    attendance_records = query.offset(skip).limit(limit).all()
    return attendance_records

@router.get("/stats", response_model=AttendanceStats)
def get_attendance_stats(db: Session = Depends(get_db)):
    """Get attendance statistics."""
    now = datetime.utcnow()
    today_start = datetime.combine(now.date(), datetime.min.time())
    week_start = today_start - timedelta(days=now.weekday())
    month_start = datetime.combine(now.date().replace(day=1), datetime.min.time())
    
    # Total attendance today
    total_today = db.query(Attendance).filter(
        Attendance.detected_at >= today_start
    ).count()
    
    # Unique users today
    unique_users_today = db.query(Attendance.user_id).filter(
        Attendance.detected_at >= today_start
    ).distinct().count()
    
    # Total attendance this week
    total_this_week = db.query(Attendance).filter(
        Attendance.detected_at >= week_start
    ).count()
    
    # Total attendance this month
    total_this_month = db.query(Attendance).filter(
        Attendance.detected_at >= month_start
    ).count()
    
    return AttendanceStats(
        total_today=total_today,
        unique_users_today=unique_users_today,
        total_this_week=total_this_week,
        total_this_month=total_this_month
    )

@router.get("/recent", response_model=List[AttendanceSchema])
def get_recent_attendance(
    limit: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """Get recent attendance records."""
    attendance_records = db.query(Attendance).join(User).order_by(
        Attendance.detected_at.desc()
    ).limit(limit).all()
    
    return attendance_records

@router.get("/user/{user_id}", response_model=List[AttendanceSchema])
def get_user_attendance(
    user_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db)
):
    """Get attendance records for a specific user."""
    # Check if user exists
    user = db.query(User).filter(User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    query = db.query(Attendance).filter(Attendance.user_id == user_id)
    
    if start_date:
        query = query.filter(Attendance.detected_at >= start_date)
    
    if end_date:
        end_datetime = datetime.combine(end_date, datetime.min.time()) + timedelta(days=1)
        query = query.filter(Attendance.detected_at < end_datetime)
    
    query = query.order_by(Attendance.detected_at.desc())
    attendance_records = query.offset(skip).limit(limit).all()
    
    return attendance_records

@router.delete("/{attendance_id}")
def delete_attendance(attendance_id: int, db: Session = Depends(get_db)):
    """Delete an attendance record."""
    attendance = db.query(Attendance).filter(Attendance.attendance_id == attendance_id).first()
    if not attendance:
        raise HTTPException(status_code=404, detail="Attendance record not found")
    
    db.delete(attendance)
    db.commit()
    
    return {"message": "Attendance record deleted successfully"}
