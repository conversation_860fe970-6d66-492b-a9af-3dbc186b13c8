# Database Configuration
# For development (SQLite)
DATABASE_URL=sqlite:///./face_recognition.db

# For production (PostgreSQL)
# DATABASE_URL=postgresql://username:password@localhost:5432/face_recognition

# Security
SECRET_KEY=your-secret-key-change-this-in-production-make-it-long-and-random
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File Upload Settings
UPLOAD_DIR=../uploads
MAX_FILE_SIZE=10485760
ALLOWED_IMAGE_EXTENSIONS=.jpg,.jpeg,.png,.bmp

# Face Recognition Settings
FACE_RECOGNITION_TOLERANCE=0.6
FACE_DETECTION_MODEL=hog

# Attendance Settings
DUPLICATE_ATTENDANCE_THRESHOLD_MINUTES=5

# Camera Settings
DEFAULT_CAMERA_INDEX=0
RTSP_TIMEOUT_SECONDS=10

# WebSocket Settings
WEBSOCKET_HEARTBEAT_INTERVAL=30
