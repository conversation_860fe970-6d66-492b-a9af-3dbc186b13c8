# Face Recognition Attendance Monitoring System

A real-time attendance monitoring application that uses face recognition technology to automatically track service attendance by analyzing live video feeds from CCTV cameras or local machine cameras.

## Features

- Real-time face detection and recognition from video streams
- Support for multiple simultaneous video feeds
- User database management with photo upload
- Attendance tracking and reporting
- Live dashboard with real-time updates
- CCTV camera and local camera support
- Export functionality (PDF, Excel, CSV)
- Configuration management for cameras and recognition parameters

## Technology Stack

### Backend
- **FastAPI**: Modern, fast web framework for building APIs
- **SQLAlchemy**: SQL toolkit and ORM
- **PostgreSQL**: Production database
- **SQLite**: Development database
- **OpenCV**: Computer vision library
- **face_recognition**: Face recognition library
- **WebSocket**: Real-time communication

### Frontend
- **React**: Frontend framework
- **Vite**: Build tool
- **Material-UI**: UI component library
- **Socket.IO**: Real-time communication

## Project Structure

```
face_recognition/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core functionality
│   │   ├── db/             # Database models and config
│   │   ├── services/       # Business logic
│   │   └── main.py         # FastAPI app entry point
│   ├── requirements.txt    # Python dependencies
│   └── alembic/           # Database migrations
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   └── utils/          # Utility functions
│   ├── package.json        # Node.js dependencies
│   └── vite.config.js      # Vite configuration
├── uploads/                # File uploads directory
├── docker-compose.yml      # Docker configuration
└── README.md              # This file
```

## Installation

### Prerequisites
- Python 3.8+
- Node.js 16+
- PostgreSQL (for production)

### Backend Setup
```bash
cd backend
pip install -r requirements.txt
alembic upgrade head
uvicorn app.main:app --reload
```

### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

## Configuration

### Environment Variables
Create a `.env` file in the backend directory:

```env
# Database
DATABASE_URL=sqlite:///./face_recognition.db  # Development
# DATABASE_URL=postgresql://user:password@localhost/face_recognition  # Production

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File Upload
UPLOAD_DIR=../uploads
MAX_FILE_SIZE=10485760  # 10MB
```

## Usage

1. Start the backend server
2. Start the frontend development server
3. Open http://localhost:3000 in your browser
4. Add users with reference photos
5. Configure cameras
6. Start monitoring attendance

## API Documentation

Once the backend is running, visit http://localhost:8000/docs for interactive API documentation.

## License

This project is licensed under the MIT License.
