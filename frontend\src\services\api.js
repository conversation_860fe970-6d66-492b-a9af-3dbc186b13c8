import axios from 'axios'

const API_BASE_URL = '/api'

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// User API
export const userAPI = {
  getUsers: (params = {}) => api.get('/users', { params }),
  getUser: (userId) => api.get(`/users/${userId}`),
  createUser: (formData) => api.post('/users', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  updateUser: (userId, formData) => api.put(`/users/${userId}`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  deleteUser: (userId) => api.delete(`/users/${userId}`),
  getUserStats: () => api.get('/users/stats'),
  reloadFaces: () => api.post('/users/reload-faces'),
}

// Attendance API
export const attendanceAPI = {
  getAttendance: (params = {}) => api.get('/attendance', { params }),
  getAttendanceStats: () => api.get('/attendance/stats'),
  getRecentAttendance: (limit = 10) => api.get('/attendance/recent', { params: { limit } }),
  getUserAttendance: (userId, params = {}) => api.get(`/attendance/user/${userId}`, { params }),
  deleteAttendance: (attendanceId) => api.delete(`/attendance/${attendanceId}`),
}

// Camera API
export const cameraAPI = {
  getCameras: (params = {}) => api.get('/cameras', { params }),
  getCamera: (cameraId) => api.get(`/cameras/${cameraId}`),
  createCamera: (cameraData) => api.post('/cameras', cameraData),
  updateCamera: (cameraId, cameraData) => api.put(`/cameras/${cameraId}`, cameraData),
  deleteCamera: (cameraId) => api.delete(`/cameras/${cameraId}`),
  startCamera: (cameraId) => api.post(`/cameras/${cameraId}/start`),
  stopCamera: (cameraId) => api.post(`/cameras/${cameraId}/stop`),
  getCameraStatus: (cameraId) => api.get(`/cameras/${cameraId}/status`),
  getAllCameraStatuses: () => api.get('/cameras/status/all'),
  startAllCameras: () => api.post('/cameras/start-all'),
  stopAllCameras: () => api.post('/cameras/stop-all'),
  testCameraConnection: (cameraId) => api.post(`/cameras/${cameraId}/test-connection`),
}

// Recognition API
export const recognitionAPI = {
  startRecognition: () => api.post('/recognition/start'),
  stopRecognition: () => api.post('/recognition/stop'),
  getRecognitionStatus: () => api.get('/recognition/status'),
  getCameraFrame: (cameraId) => api.get(`/recognition/cameras/${cameraId}/frame`),
  reloadFaces: () => api.post('/recognition/reload-faces'),
}

// Export default api instance for custom requests
export default api
