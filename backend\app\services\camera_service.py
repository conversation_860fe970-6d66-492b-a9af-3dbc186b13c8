import cv2
import threading
import time
import logging
from typing import Dict, Optional, Callable, Any
from dataclasses import dataclass
from app.core.config import settings

logger = logging.getLogger(__name__)

@dataclass
class CameraStatus:
    is_connected: bool = False
    last_frame_time: Optional[float] = None
    error_message: Optional[str] = None
    frame_count: int = 0
    fps: float = 0.0

class CameraStream:
    def __init__(self, camera_id: str, connection_string: str, camera_type: str = "local"):
        self.camera_id = camera_id
        self.connection_string = connection_string
        self.camera_type = camera_type
        self.cap: Optional[cv2.VideoCapture] = None
        self.is_running = False
        self.thread: Optional[threading.Thread] = None
        self.status = CameraStatus()
        self.frame_callbacks: List[Callable] = []
        self.current_frame = None
        self.frame_lock = threading.Lock()
        
    def connect(self) -> bool:
        """Connect to the camera stream."""
        try:
            if self.camera_type == "local":
                # For local cameras, connection_string should be an integer (camera index)
                camera_index = int(self.connection_string)
                self.cap = cv2.VideoCapture(camera_index)
            else:  # RTSP or HTTP stream
                self.cap = cv2.VideoCapture(self.connection_string)
            
            if not self.cap.isOpened():
                self.status.error_message = "Failed to open camera stream"
                return False
            
            # Test if we can read a frame
            ret, frame = self.cap.read()
            if not ret:
                self.status.error_message = "Failed to read frame from camera"
                self.cap.release()
                self.cap = None
                return False
            
            self.status.is_connected = True
            self.status.error_message = None
            logger.info(f"Successfully connected to camera {self.camera_id}")
            return True
            
        except Exception as e:
            self.status.error_message = f"Connection error: {str(e)}"
            logger.error(f"Error connecting to camera {self.camera_id}: {str(e)}")
            return False
    
    def disconnect(self):
        """Disconnect from the camera stream."""
        self.stop()
        if self.cap:
            self.cap.release()
            self.cap = None
        self.status.is_connected = False
        logger.info(f"Disconnected from camera {self.camera_id}")
    
    def start_streaming(self):
        """Start the video streaming thread."""
        if self.is_running:
            return
        
        if not self.status.is_connected:
            if not self.connect():
                return
        
        self.is_running = True
        self.thread = threading.Thread(target=self._stream_loop, daemon=True)
        self.thread.start()
        logger.info(f"Started streaming for camera {self.camera_id}")
    
    def stop(self):
        """Stop the video streaming."""
        self.is_running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5.0)
        logger.info(f"Stopped streaming for camera {self.camera_id}")
    
    def _stream_loop(self):
        """Main streaming loop."""
        frame_times = []
        
        while self.is_running and self.cap:
            try:
                ret, frame = self.cap.read()
                
                if not ret:
                    self.status.error_message = "Failed to read frame"
                    logger.warning(f"Failed to read frame from camera {self.camera_id}")
                    
                    # Try to reconnect
                    if self._attempt_reconnection():
                        continue
                    else:
                        break
                
                current_time = time.time()
                
                # Update status
                with self.frame_lock:
                    self.current_frame = frame.copy()
                    self.status.last_frame_time = current_time
                    self.status.frame_count += 1
                    self.status.error_message = None
                
                # Calculate FPS
                frame_times.append(current_time)
                if len(frame_times) > 30:  # Keep last 30 frame times
                    frame_times.pop(0)
                
                if len(frame_times) > 1:
                    time_diff = frame_times[-1] - frame_times[0]
                    self.status.fps = (len(frame_times) - 1) / time_diff
                
                # Call frame callbacks
                for callback in self.frame_callbacks:
                    try:
                        callback(self.camera_id, frame)
                    except Exception as e:
                        logger.error(f"Error in frame callback for camera {self.camera_id}: {str(e)}")
                
                # Small delay to prevent excessive CPU usage
                time.sleep(0.01)
                
            except Exception as e:
                self.status.error_message = f"Streaming error: {str(e)}"
                logger.error(f"Error in streaming loop for camera {self.camera_id}: {str(e)}")
                break
        
        self.is_running = False
        logger.info(f"Streaming loop ended for camera {self.camera_id}")
    
    def _attempt_reconnection(self) -> bool:
        """Attempt to reconnect to the camera."""
        logger.info(f"Attempting to reconnect camera {self.camera_id}")
        
        if self.cap:
            self.cap.release()
        
        time.sleep(2)  # Wait before reconnecting
        
        return self.connect()
    
    def add_frame_callback(self, callback: Callable):
        """Add a callback function to be called for each frame."""
        self.frame_callbacks.append(callback)
    
    def remove_frame_callback(self, callback: Callable):
        """Remove a frame callback."""
        if callback in self.frame_callbacks:
            self.frame_callbacks.remove(callback)
    
    def get_current_frame(self):
        """Get the current frame (thread-safe)."""
        with self.frame_lock:
            return self.current_frame.copy() if self.current_frame is not None else None

class CameraManager:
    def __init__(self):
        self.cameras: Dict[str, CameraStream] = {}
        self.global_frame_callbacks: List[Callable] = []
    
    def add_camera(self, camera_id: str, connection_string: str, camera_type: str = "local") -> bool:
        """Add a new camera to the manager."""
        if camera_id in self.cameras:
            logger.warning(f"Camera {camera_id} already exists")
            return False
        
        camera_stream = CameraStream(camera_id, connection_string, camera_type)
        
        # Add global callbacks to the camera
        for callback in self.global_frame_callbacks:
            camera_stream.add_frame_callback(callback)
        
        self.cameras[camera_id] = camera_stream
        logger.info(f"Added camera {camera_id}")
        return True
    
    def remove_camera(self, camera_id: str) -> bool:
        """Remove a camera from the manager."""
        if camera_id not in self.cameras:
            return False
        
        camera = self.cameras[camera_id]
        camera.stop()
        camera.disconnect()
        del self.cameras[camera_id]
        logger.info(f"Removed camera {camera_id}")
        return True
    
    def start_camera(self, camera_id: str) -> bool:
        """Start streaming for a specific camera."""
        if camera_id not in self.cameras:
            return False
        
        self.cameras[camera_id].start_streaming()
        return True
    
    def stop_camera(self, camera_id: str) -> bool:
        """Stop streaming for a specific camera."""
        if camera_id not in self.cameras:
            return False
        
        self.cameras[camera_id].stop()
        return True
    
    def start_all_cameras(self):
        """Start streaming for all cameras."""
        for camera in self.cameras.values():
            camera.start_streaming()
    
    def stop_all_cameras(self):
        """Stop streaming for all cameras."""
        for camera in self.cameras.values():
            camera.stop()
    
    def get_camera_status(self, camera_id: str) -> Optional[CameraStatus]:
        """Get the status of a specific camera."""
        if camera_id not in self.cameras:
            return None
        return self.cameras[camera_id].status
    
    def get_all_camera_statuses(self) -> Dict[str, CameraStatus]:
        """Get the status of all cameras."""
        return {camera_id: camera.status for camera_id, camera in self.cameras.items()}
    
    def get_current_frame(self, camera_id: str):
        """Get the current frame from a specific camera."""
        if camera_id not in self.cameras:
            return None
        return self.cameras[camera_id].get_current_frame()
    
    def add_global_frame_callback(self, callback: Callable):
        """Add a callback that will be called for frames from all cameras."""
        self.global_frame_callbacks.append(callback)
        
        # Add to existing cameras
        for camera in self.cameras.values():
            camera.add_frame_callback(callback)
    
    def remove_global_frame_callback(self, callback: Callable):
        """Remove a global frame callback."""
        if callback in self.global_frame_callbacks:
            self.global_frame_callbacks.remove(callback)
        
        # Remove from existing cameras
        for camera in self.cameras.values():
            camera.remove_frame_callback(callback)

# Global camera manager instance
camera_manager = CameraManager()
