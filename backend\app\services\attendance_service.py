import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, func

from app.db.models import Attendance, User
from app.core.config import settings

logger = logging.getLogger(__name__)

class AttendanceTracker:
    def __init__(self):
        self.recent_detections: Dict[str, datetime] = {}  # user_id:camera_id -> last_detection_time
        self.detection_cache_duration = timedelta(minutes=settings.DUPLICATE_ATTENDANCE_THRESHOLD_MINUTES)
    
    def should_record_attendance(self, user_id: int, camera_id: str, db: Session) -> bool:
        """
        Check if attendance should be recorded based on duplicate prevention rules.
        
        Args:
            user_id: User ID
            camera_id: Camera ID
            db: Database session
            
        Returns:
            True if attendance should be recorded, False otherwise
        """
        cache_key = f"{user_id}:{camera_id}"
        current_time = datetime.utcnow()
        
        # Check in-memory cache first (for performance)
        if cache_key in self.recent_detections:
            last_detection = self.recent_detections[cache_key]
            if current_time - last_detection < self.detection_cache_duration:
                return False
        
        # Check database for recent attendance
        threshold_time = current_time - self.detection_cache_duration
        
        existing_attendance = db.query(Attendance).filter(
            and_(
                Attendance.user_id == user_id,
                Attendance.camera_id == camera_id,
                Attendance.detected_at >= threshold_time
            )
        ).first()
        
        if existing_attendance:
            # Update cache
            self.recent_detections[cache_key] = existing_attendance.detected_at
            return False
        
        return True
    
    def record_attendance(
        self,
        user_id: int,
        camera_id: str,
        confidence_score: float,
        db: Session,
        detection_photo_path: Optional[str] = None,
        location: Optional[str] = None
    ) -> Optional[Attendance]:
        """
        Record attendance if it passes duplicate prevention checks.
        
        Args:
            user_id: User ID
            camera_id: Camera ID
            confidence_score: Face recognition confidence score
            db: Database session
            detection_photo_path: Optional path to detection photo
            location: Optional location description
            
        Returns:
            Attendance record if created, None if duplicate
        """
        if not self.should_record_attendance(user_id, camera_id, db):
            logger.debug(f"Duplicate attendance prevented for user {user_id} on camera {camera_id}")
            return None
        
        try:
            # Create attendance record
            attendance = Attendance(
                user_id=user_id,
                camera_id=camera_id,
                confidence_score=confidence_score,
                detection_photo_path=detection_photo_path,
                location=location
            )
            
            db.add(attendance)
            db.commit()
            db.refresh(attendance)
            
            # Update cache
            cache_key = f"{user_id}:{camera_id}"
            self.recent_detections[cache_key] = attendance.detected_at
            
            logger.info(f"Attendance recorded: User {user_id}, Camera {camera_id}, Confidence {confidence_score:.2f}")
            return attendance
            
        except Exception as e:
            logger.error(f"Error recording attendance: {str(e)}")
            db.rollback()
            return None
    
    def cleanup_cache(self):
        """Remove old entries from the detection cache."""
        current_time = datetime.utcnow()
        expired_keys = []
        
        for cache_key, detection_time in self.recent_detections.items():
            if current_time - detection_time > self.detection_cache_duration:
                expired_keys.append(cache_key)
        
        for key in expired_keys:
            del self.recent_detections[key]
        
        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    def get_attendance_stats(self, db: Session) -> Dict[str, int]:
        """Get attendance statistics for different time periods."""
        now = datetime.utcnow()
        today_start = datetime.combine(now.date(), datetime.min.time())
        week_start = today_start - timedelta(days=now.weekday())
        month_start = datetime.combine(now.date().replace(day=1), datetime.min.time())
        
        stats = {}
        
        # Today's stats
        stats['total_today'] = db.query(Attendance).filter(
            Attendance.detected_at >= today_start
        ).count()
        
        stats['unique_users_today'] = db.query(Attendance.user_id).filter(
            Attendance.detected_at >= today_start
        ).distinct().count()
        
        # This week's stats
        stats['total_this_week'] = db.query(Attendance).filter(
            Attendance.detected_at >= week_start
        ).count()
        
        stats['unique_users_this_week'] = db.query(Attendance.user_id).filter(
            Attendance.detected_at >= week_start
        ).distinct().count()
        
        # This month's stats
        stats['total_this_month'] = db.query(Attendance).filter(
            Attendance.detected_at >= month_start
        ).count()
        
        stats['unique_users_this_month'] = db.query(Attendance.user_id).filter(
            Attendance.detected_at >= month_start
        ).distinct().count()
        
        return stats
    
    def get_user_attendance_summary(self, user_id: int, db: Session, days: int = 30) -> Dict[str, any]:
        """Get attendance summary for a specific user."""
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Get all attendance records for the user in the specified period
        attendance_records = db.query(Attendance).filter(
            and_(
                Attendance.user_id == user_id,
                Attendance.detected_at >= start_date
            )
        ).order_by(Attendance.detected_at.desc()).all()
        
        # Calculate statistics
        total_detections = len(attendance_records)
        unique_days = len(set(record.detected_at.date() for record in attendance_records))
        
        # Group by camera
        camera_stats = {}
        for record in attendance_records:
            camera_id = record.camera_id
            if camera_id not in camera_stats:
                camera_stats[camera_id] = {
                    'count': 0,
                    'first_detection': record.detected_at,
                    'last_detection': record.detected_at,
                    'avg_confidence': 0.0
                }
            
            camera_stats[camera_id]['count'] += 1
            camera_stats[camera_id]['avg_confidence'] += record.confidence_score
            
            if record.detected_at < camera_stats[camera_id]['first_detection']:
                camera_stats[camera_id]['first_detection'] = record.detected_at
            if record.detected_at > camera_stats[camera_id]['last_detection']:
                camera_stats[camera_id]['last_detection'] = record.detected_at
        
        # Calculate average confidence for each camera
        for camera_id in camera_stats:
            count = camera_stats[camera_id]['count']
            camera_stats[camera_id]['avg_confidence'] /= count
        
        return {
            'user_id': user_id,
            'period_days': days,
            'total_detections': total_detections,
            'unique_days_present': unique_days,
            'attendance_rate': unique_days / days if days > 0 else 0,
            'camera_stats': camera_stats,
            'recent_attendance': [
                {
                    'attendance_id': record.attendance_id,
                    'camera_id': record.camera_id,
                    'detected_at': record.detected_at.isoformat(),
                    'confidence_score': record.confidence_score
                }
                for record in attendance_records[:10]  # Last 10 records
            ]
        }
    
    def get_camera_activity(self, camera_id: str, db: Session, hours: int = 24) -> Dict[str, any]:
        """Get activity statistics for a specific camera."""
        start_time = datetime.utcnow() - timedelta(hours=hours)
        
        # Get all attendance records for the camera in the specified period
        attendance_records = db.query(Attendance).join(User).filter(
            and_(
                Attendance.camera_id == camera_id,
                Attendance.detected_at >= start_time
            )
        ).order_by(Attendance.detected_at.desc()).all()
        
        total_detections = len(attendance_records)
        unique_users = len(set(record.user_id for record in attendance_records))
        
        # Calculate hourly distribution
        hourly_stats = {}
        for record in attendance_records:
            hour = record.detected_at.hour
            if hour not in hourly_stats:
                hourly_stats[hour] = 0
            hourly_stats[hour] += 1
        
        # Get user frequency
        user_frequency = {}
        for record in attendance_records:
            user_id = record.user_id
            if user_id not in user_frequency:
                user_frequency[user_id] = {
                    'count': 0,
                    'user_name': f"{record.user.first_name} {record.user.last_name or ''}".strip(),
                    'avg_confidence': 0.0
                }
            user_frequency[user_id]['count'] += 1
            user_frequency[user_id]['avg_confidence'] += record.confidence_score
        
        # Calculate average confidence for each user
        for user_id in user_frequency:
            count = user_frequency[user_id]['count']
            user_frequency[user_id]['avg_confidence'] /= count
        
        return {
            'camera_id': camera_id,
            'period_hours': hours,
            'total_detections': total_detections,
            'unique_users': unique_users,
            'hourly_distribution': hourly_stats,
            'user_frequency': user_frequency,
            'recent_detections': [
                {
                    'attendance_id': record.attendance_id,
                    'user_id': record.user_id,
                    'user_name': f"{record.user.first_name} {record.user.last_name or ''}".strip(),
                    'detected_at': record.detected_at.isoformat(),
                    'confidence_score': record.confidence_score
                }
                for record in attendance_records[:20]  # Last 20 records
            ]
        }

# Global attendance tracker instance
attendance_tracker = AttendanceTracker()
