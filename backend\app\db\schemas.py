from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

# User schemas
class UserBase(BaseModel):
    first_name: str = Field(..., min_length=1, max_length=100)
    middle_names: Optional[str] = Field(None, max_length=200)
    last_name: Optional[str] = Field(None, max_length=100)
    date_of_birth: Optional[datetime] = None
    department: Optional[str] = Field(None, max_length=100)
    role: Optional[str] = Field(None, max_length=100)
    contact_info: Optional[str] = Field(None, max_length=200)

class UserCreate(UserBase):
    pass

class UserUpdate(BaseModel):
    first_name: Optional[str] = Field(None, min_length=1, max_length=100)
    middle_names: Optional[str] = Field(None, max_length=200)
    last_name: Optional[str] = Field(None, max_length=100)
    date_of_birth: Optional[datetime] = None
    department: Optional[str] = Field(None, max_length=100)
    role: Optional[str] = Field(None, max_length=100)
    contact_info: Optional[str] = Field(None, max_length=200)
    is_active: Optional[bool] = None

class User(UserBase):
    user_id: int
    reference_photo_path: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_active: bool

    class Config:
        from_attributes = True

# Attendance schemas
class AttendanceBase(BaseModel):
    camera_id: str
    confidence_score: float = Field(..., ge=0.0, le=1.0)
    location: Optional[str] = None

class AttendanceCreate(AttendanceBase):
    user_id: int

class Attendance(AttendanceBase):
    attendance_id: int
    user_id: int
    detected_at: datetime
    detection_photo_path: Optional[str] = None
    user: Optional[User] = None

    class Config:
        from_attributes = True

# Camera schemas
class CameraBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    camera_type: str = Field(..., regex="^(local|rtsp)$")
    connection_string: str = Field(..., min_length=1, max_length=500)
    location: Optional[str] = Field(None, max_length=200)
    resolution_width: int = Field(640, ge=320, le=1920)
    resolution_height: int = Field(480, ge=240, le=1080)
    frame_rate: int = Field(30, ge=1, le=60)
    username: Optional[str] = Field(None, max_length=100)
    password: Optional[str] = Field(None, max_length=100)

class CameraCreate(CameraBase):
    camera_id: str = Field(..., min_length=1, max_length=100)

class CameraUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    camera_type: Optional[str] = Field(None, regex="^(local|rtsp)$")
    connection_string: Optional[str] = Field(None, min_length=1, max_length=500)
    location: Optional[str] = Field(None, max_length=200)
    is_active: Optional[bool] = None
    resolution_width: Optional[int] = Field(None, ge=320, le=1920)
    resolution_height: Optional[int] = Field(None, ge=240, le=1080)
    frame_rate: Optional[int] = Field(None, ge=1, le=60)
    username: Optional[str] = Field(None, max_length=100)
    password: Optional[str] = Field(None, max_length=100)

class Camera(CameraBase):
    camera_id: str
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# System settings schemas
class SystemSettingBase(BaseModel):
    setting_key: str = Field(..., min_length=1, max_length=100)
    setting_value: str
    description: Optional[str] = None

class SystemSettingCreate(SystemSettingBase):
    pass

class SystemSettingUpdate(BaseModel):
    setting_value: str
    description: Optional[str] = None

class SystemSetting(SystemSettingBase):
    setting_id: int
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# Response schemas
class AttendanceStats(BaseModel):
    total_today: int
    unique_users_today: int
    total_this_week: int
    total_this_month: int

class UserStats(BaseModel):
    total_users: int
    active_users: int
    inactive_users: int
