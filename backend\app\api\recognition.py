from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
import json
import asyncio
import cv2
import numpy as np
from typing import List, Dict, Any
import logging
from datetime import datetime

from app.db.database import get_db
from app.db.models import User, Attendance
from app.services.face_recognition_service import face_recognition_service
from app.services.camera_service import camera_manager
from app.core.config import settings

router = APIRouter()
logger = logging.getLogger(__name__)

# Store active WebSocket connections
active_connections: List[WebSocket] = []

class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        try:
            await websocket.send_text(message)
        except:
            self.disconnect(websocket)

    async def broadcast(self, message: str):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                disconnected.append(connection)
        
        # Remove disconnected connections
        for connection in disconnected:
            self.disconnect(connection)

manager = ConnectionManager()

def process_frame_for_recognition(camera_id: str, frame: np.ndarray):
    """Process a frame for face recognition and create attendance records."""
    try:
        # Detect faces in the frame
        detected_faces = face_recognition_service.detect_faces_in_frame(frame)
        
        if detected_faces:
            # Get database session
            from app.db.database import SessionLocal
            db = SessionLocal()
            
            try:
                for face_data in detected_faces:
                    user_id = face_data.get('user_id')
                    confidence = face_data.get('confidence', 0.0)
                    
                    if user_id and confidence > 0.5:  # Minimum confidence threshold
                        # Check for duplicate attendance
                        from datetime import timedelta
                        threshold_time = datetime.utcnow() - timedelta(minutes=settings.DUPLICATE_ATTENDANCE_THRESHOLD_MINUTES)
                        
                        existing_attendance = db.query(Attendance).filter(
                            Attendance.user_id == user_id,
                            Attendance.camera_id == camera_id,
                            Attendance.detected_at >= threshold_time
                        ).first()
                        
                        if not existing_attendance:
                            # Create new attendance record
                            attendance = Attendance(
                                user_id=user_id,
                                camera_id=camera_id,
                                confidence_score=confidence
                            )
                            db.add(attendance)
                            db.commit()
                            db.refresh(attendance)
                            
                            # Get user details
                            user = db.query(User).filter(User.user_id == user_id).first()
                            
                            # Broadcast attendance event via WebSocket
                            attendance_event = {
                                "type": "attendance_detected",
                                "data": {
                                    "attendance_id": attendance.attendance_id,
                                    "user_id": user_id,
                                    "user_name": f"{user.first_name} {user.last_name or ''}".strip() if user else "Unknown",
                                    "camera_id": camera_id,
                                    "confidence_score": confidence,
                                    "detected_at": attendance.detected_at.isoformat()
                                }
                            }
                            
                            # Send to all connected WebSocket clients
                            asyncio.create_task(manager.broadcast(json.dumps(attendance_event)))
                            
                            logger.info(f"Attendance recorded for user {user_id} on camera {camera_id}")
            
            finally:
                db.close()
                
    except Exception as e:
        logger.error(f"Error processing frame for recognition: {str(e)}")

@router.post("/start")
async def start_recognition():
    """Start face recognition on all active cameras."""
    
    # Load known faces from database
    from app.db.database import SessionLocal
    db = SessionLocal()
    
    try:
        users = db.query(User).filter(User.is_active == True, User.face_encoding.isnot(None)).all()
        
        users_data = []
        for user in users:
            users_data.append({
                'user_id': user.user_id,
                'face_encoding': user.face_encoding
            })
        
        face_recognition_service.load_known_faces(users_data)
        
        # Add frame processing callback to camera manager
        camera_manager.add_global_frame_callback(process_frame_for_recognition)
        
        # Start all cameras
        camera_manager.start_all_cameras()
        
        return {
            "message": "Face recognition started",
            "loaded_faces": len(users_data),
            "active_cameras": len(camera_manager.cameras)
        }
        
    finally:
        db.close()

@router.post("/stop")
async def stop_recognition():
    """Stop face recognition."""
    
    # Remove frame processing callback
    camera_manager.remove_global_frame_callback(process_frame_for_recognition)
    
    # Stop all cameras
    camera_manager.stop_all_cameras()
    
    return {"message": "Face recognition stopped"}

@router.get("/status")
async def get_recognition_status():
    """Get the current status of face recognition system."""
    
    camera_statuses = camera_manager.get_all_camera_statuses()
    
    return {
        "is_running": len([s for s in camera_statuses.values() if s.is_connected]) > 0,
        "loaded_faces": len(face_recognition_service.known_face_encodings),
        "active_cameras": len([s for s in camera_statuses.values() if s.is_connected]),
        "camera_statuses": {
            camera_id: {
                "is_connected": status.is_connected,
                "fps": status.fps,
                "frame_count": status.frame_count,
                "error_message": status.error_message
            }
            for camera_id, status in camera_statuses.items()
        }
    }

@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates."""
    await manager.connect(websocket)
    
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            
            # Handle ping/pong for connection health
            if data == "ping":
                await websocket.send_text("pong")
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {str(e)}")
        manager.disconnect(websocket)

@router.get("/cameras/{camera_id}/frame")
async def get_camera_frame(camera_id: str):
    """Get the current frame from a specific camera (for debugging)."""
    
    frame = camera_manager.get_current_frame(camera_id)
    
    if frame is None:
        raise HTTPException(status_code=404, detail="No frame available from camera")
    
    # Convert frame to base64 for JSON response
    import base64
    
    # Encode frame as JPEG
    _, buffer = cv2.imencode('.jpg', frame)
    frame_base64 = base64.b64encode(buffer).decode('utf-8')
    
    return {
        "camera_id": camera_id,
        "frame": frame_base64,
        "timestamp": datetime.utcnow().isoformat()
    }

@router.post("/reload-faces")
async def reload_faces():
    """Reload face encodings from database."""
    
    from app.db.database import SessionLocal
    db = SessionLocal()
    
    try:
        users = db.query(User).filter(User.is_active == True, User.face_encoding.isnot(None)).all()
        
        users_data = []
        for user in users:
            users_data.append({
                'user_id': user.user_id,
                'face_encoding': user.face_encoding
            })
        
        face_recognition_service.load_known_faces(users_data)
        
        return {
            "message": "Face encodings reloaded",
            "loaded_faces": len(users_data)
        }
        
    finally:
        db.close()
