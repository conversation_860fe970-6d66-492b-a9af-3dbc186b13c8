import face_recognition
import cv2
import numpy as np
import pickle
from typing import List, Tuple, Optional, Dict, Any
from pathlib import Path
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)

class FaceRecognitionService:
    def __init__(self):
        self.known_face_encodings: List[np.ndarray] = []
        self.known_face_user_ids: List[int] = []
        self.tolerance = settings.FACE_RECOGNITION_TOLERANCE
        self.model = settings.FACE_DETECTION_MODEL
        
    def encode_face_from_image(self, image_path: str) -> Optional[np.ndarray]:
        """
        Extract face encoding from an image file.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Face encoding as numpy array or None if no face found
        """
        try:
            # Load image
            image = face_recognition.load_image_from_file(image_path)
            
            # Find face locations
            face_locations = face_recognition.face_locations(image, model=self.model)
            
            if not face_locations:
                logger.warning(f"No face found in image: {image_path}")
                return None
                
            if len(face_locations) > 1:
                logger.warning(f"Multiple faces found in image: {image_path}. Using the first one.")
            
            # Get face encodings
            face_encodings = face_recognition.face_encodings(image, face_locations)
            
            if face_encodings:
                return face_encodings[0]
            else:
                logger.warning(f"Could not encode face in image: {image_path}")
                return None
                
        except Exception as e:
            logger.error(f"Error encoding face from image {image_path}: {str(e)}")
            return None
    
    def encode_face_from_array(self, image_array: np.ndarray) -> Optional[np.ndarray]:
        """
        Extract face encoding from an image array.
        
        Args:
            image_array: Image as numpy array (RGB format)
            
        Returns:
            Face encoding as numpy array or None if no face found
        """
        try:
            # Find face locations
            face_locations = face_recognition.face_locations(image_array, model=self.model)
            
            if not face_locations:
                return None
                
            # Get face encodings
            face_encodings = face_recognition.face_encodings(image_array, face_locations)
            
            if face_encodings:
                return face_encodings[0]
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error encoding face from array: {str(e)}")
            return None
    
    def detect_faces_in_frame(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """
        Detect and recognize faces in a video frame.
        
        Args:
            frame: Video frame as numpy array (BGR format from OpenCV)
            
        Returns:
            List of detected faces with their information
        """
        try:
            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Resize frame for faster processing (optional)
            small_frame = cv2.resize(rgb_frame, (0, 0), fx=0.25, fy=0.25)
            
            # Find face locations and encodings
            face_locations = face_recognition.face_locations(small_frame, model=self.model)
            face_encodings = face_recognition.face_encodings(small_frame, face_locations)
            
            detected_faces = []
            
            for face_encoding, face_location in zip(face_encodings, face_locations):
                # Scale back up face locations since the frame we detected in was scaled to 1/4 size
                top, right, bottom, left = face_location
                top *= 4
                right *= 4
                bottom *= 4
                left *= 4
                
                # Try to match the face
                user_id, confidence = self.match_face(face_encoding)
                
                detected_faces.append({
                    'location': (top, right, bottom, left),
                    'encoding': face_encoding,
                    'user_id': user_id,
                    'confidence': confidence
                })
            
            return detected_faces
            
        except Exception as e:
            logger.error(f"Error detecting faces in frame: {str(e)}")
            return []
    
    def match_face(self, face_encoding: np.ndarray) -> Tuple[Optional[int], float]:
        """
        Match a face encoding against known faces.
        
        Args:
            face_encoding: Face encoding to match
            
        Returns:
            Tuple of (user_id, confidence) or (None, 0.0) if no match
        """
        if not self.known_face_encodings:
            return None, 0.0
        
        try:
            # Calculate distances to all known faces
            face_distances = face_recognition.face_distance(self.known_face_encodings, face_encoding)
            
            # Find the best match
            best_match_index = np.argmin(face_distances)
            best_distance = face_distances[best_match_index]
            
            # Check if the best match is within tolerance
            if best_distance <= self.tolerance:
                user_id = self.known_face_user_ids[best_match_index]
                confidence = 1.0 - best_distance  # Convert distance to confidence
                return user_id, confidence
            else:
                return None, 0.0
                
        except Exception as e:
            logger.error(f"Error matching face: {str(e)}")
            return None, 0.0
    
    def load_known_faces(self, users_data: List[Dict[str, Any]]):
        """
        Load known faces from user data.
        
        Args:
            users_data: List of user dictionaries with 'user_id' and 'face_encoding'
        """
        self.known_face_encodings = []
        self.known_face_user_ids = []
        
        for user_data in users_data:
            if user_data.get('face_encoding'):
                try:
                    # Deserialize face encoding
                    face_encoding = pickle.loads(user_data['face_encoding'])
                    self.known_face_encodings.append(face_encoding)
                    self.known_face_user_ids.append(user_data['user_id'])
                except Exception as e:
                    logger.error(f"Error loading face encoding for user {user_data['user_id']}: {str(e)}")
        
        logger.info(f"Loaded {len(self.known_face_encodings)} known faces")
    
    def add_known_face(self, user_id: int, face_encoding: np.ndarray):
        """
        Add a new known face to the recognition system.
        
        Args:
            user_id: User ID
            face_encoding: Face encoding as numpy array
        """
        # Remove existing encoding for this user if it exists
        self.remove_known_face(user_id)
        
        # Add new encoding
        self.known_face_encodings.append(face_encoding)
        self.known_face_user_ids.append(user_id)
        
        logger.info(f"Added face encoding for user {user_id}")
    
    def remove_known_face(self, user_id: int):
        """
        Remove a known face from the recognition system.
        
        Args:
            user_id: User ID to remove
        """
        indices_to_remove = [i for i, uid in enumerate(self.known_face_user_ids) if uid == user_id]
        
        for index in reversed(indices_to_remove):
            del self.known_face_encodings[index]
            del self.known_face_user_ids[index]
        
        if indices_to_remove:
            logger.info(f"Removed face encoding for user {user_id}")
    
    def serialize_face_encoding(self, face_encoding: np.ndarray) -> bytes:
        """
        Serialize face encoding for database storage.
        
        Args:
            face_encoding: Face encoding as numpy array
            
        Returns:
            Serialized face encoding as bytes
        """
        return pickle.dumps(face_encoding)
    
    def deserialize_face_encoding(self, serialized_encoding: bytes) -> np.ndarray:
        """
        Deserialize face encoding from database.
        
        Args:
            serialized_encoding: Serialized face encoding as bytes
            
        Returns:
            Face encoding as numpy array
        """
        return pickle.loads(serialized_encoding)

# Global instance
face_recognition_service = FaceRecognitionService()
