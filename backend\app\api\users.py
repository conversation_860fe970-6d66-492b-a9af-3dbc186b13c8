from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import uuid
from pathlib import Path
import shutil

from app.db.database import get_db
from app.db.models import User
from app.db.schemas import User as UserSchema, UserCreate, UserUpdate, UserStats
from app.core.config import settings
from app.services.face_recognition_service import face_recognition_service

router = APIRouter()

def save_uploaded_file(upload_file: UploadFile, user_id: int) -> str:
    """Save uploaded file and return the file path."""
    # Check file extension
    file_extension = Path(upload_file.filename).suffix.lower()
    if file_extension not in settings.ALLOWED_IMAGE_EXTENSIONS:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid file type. Allowed types: {', '.join(settings.ALLOWED_IMAGE_EXTENSIONS)}"
        )
    
    # Generate unique filename
    filename = f"user_{user_id}_{uuid.uuid4()}{file_extension}"
    file_path = Path(settings.UPLOAD_DIR) / "users" / filename
    
    # Create directory if it doesn't exist
    file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Save file
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(upload_file.file, buffer)
    
    return str(file_path)

@router.post("/", response_model=UserSchema)
async def create_user(
    first_name: str = Form(...),
    middle_names: Optional[str] = Form(None),
    last_name: Optional[str] = Form(None),
    department: Optional[str] = Form(None),
    role: Optional[str] = Form(None),
    contact_info: Optional[str] = Form(None),
    photo: Optional[UploadFile] = File(None),
    db: Session = Depends(get_db)
):
    """Create a new user with optional photo upload."""
    
    # Create user in database first
    user_data = UserCreate(
        first_name=first_name,
        middle_names=middle_names,
        last_name=last_name,
        department=department,
        role=role,
        contact_info=contact_info
    )
    
    db_user = User(**user_data.dict())
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    # Handle photo upload if provided
    if photo:
        try:
            # Save the uploaded file
            file_path = save_uploaded_file(photo, db_user.user_id)
            
            # Generate face encoding
            face_encoding = face_recognition_service.encode_face_from_image(file_path)
            
            if face_encoding is not None:
                # Update user with photo path and face encoding
                db_user.reference_photo_path = file_path
                db_user.face_encoding = face_recognition_service.serialize_face_encoding(face_encoding)
                
                # Add to face recognition service
                face_recognition_service.add_known_face(db_user.user_id, face_encoding)
                
                db.commit()
                db.refresh(db_user)
            else:
                # Remove the uploaded file if face encoding failed
                os.remove(file_path)
                raise HTTPException(
                    status_code=400,
                    detail="No face detected in the uploaded image. Please upload a clear photo with a visible face."
                )
                
        except Exception as e:
            # If anything goes wrong, delete the user and raise error
            db.delete(db_user)
            db.commit()
            raise HTTPException(status_code=400, detail=str(e))
    
    return db_user

@router.get("/", response_model=List[UserSchema])
def get_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None),
    db: Session = Depends(get_db)
):
    """Get list of users with optional filtering."""
    query = db.query(User)
    
    if search:
        search_filter = f"%{search}%"
        query = query.filter(
            (User.first_name.ilike(search_filter)) |
            (User.last_name.ilike(search_filter)) |
            (User.middle_names.ilike(search_filter))
        )
    
    if is_active is not None:
        query = query.filter(User.is_active == is_active)
    
    users = query.offset(skip).limit(limit).all()
    return users

@router.get("/stats", response_model=UserStats)
def get_user_stats(db: Session = Depends(get_db)):
    """Get user statistics."""
    total_users = db.query(User).count()
    active_users = db.query(User).filter(User.is_active == True).count()
    inactive_users = total_users - active_users
    
    return UserStats(
        total_users=total_users,
        active_users=active_users,
        inactive_users=inactive_users
    )

@router.get("/{user_id}", response_model=UserSchema)
def get_user(user_id: int, db: Session = Depends(get_db)):
    """Get a specific user by ID."""
    user = db.query(User).filter(User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@router.put("/{user_id}", response_model=UserSchema)
async def update_user(
    user_id: int,
    first_name: Optional[str] = Form(None),
    middle_names: Optional[str] = Form(None),
    last_name: Optional[str] = Form(None),
    department: Optional[str] = Form(None),
    role: Optional[str] = Form(None),
    contact_info: Optional[str] = Form(None),
    is_active: Optional[bool] = Form(None),
    photo: Optional[UploadFile] = File(None),
    db: Session = Depends(get_db)
):
    """Update a user with optional photo upload."""
    
    # Get existing user
    db_user = db.query(User).filter(User.user_id == user_id).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Update basic fields
    if first_name is not None:
        db_user.first_name = first_name
    if middle_names is not None:
        db_user.middle_names = middle_names
    if last_name is not None:
        db_user.last_name = last_name
    if department is not None:
        db_user.department = department
    if role is not None:
        db_user.role = role
    if contact_info is not None:
        db_user.contact_info = contact_info
    if is_active is not None:
        db_user.is_active = is_active
        
        # Remove from face recognition if deactivated
        if not is_active:
            face_recognition_service.remove_known_face(user_id)
    
    # Handle photo update if provided
    if photo:
        try:
            # Remove old photo if exists
            if db_user.reference_photo_path and os.path.exists(db_user.reference_photo_path):
                os.remove(db_user.reference_photo_path)
            
            # Save new photo
            file_path = save_uploaded_file(photo, user_id)
            
            # Generate new face encoding
            face_encoding = face_recognition_service.encode_face_from_image(file_path)
            
            if face_encoding is not None:
                # Update user with new photo and encoding
                db_user.reference_photo_path = file_path
                db_user.face_encoding = face_recognition_service.serialize_face_encoding(face_encoding)
                
                # Update face recognition service
                face_recognition_service.add_known_face(user_id, face_encoding)
            else:
                # Remove the uploaded file if face encoding failed
                os.remove(file_path)
                raise HTTPException(
                    status_code=400,
                    detail="No face detected in the uploaded image. Please upload a clear photo with a visible face."
                )
                
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))
    
    db.commit()
    db.refresh(db_user)
    return db_user

@router.delete("/{user_id}")
def delete_user(user_id: int, db: Session = Depends(get_db)):
    """Delete a user."""
    db_user = db.query(User).filter(User.user_id == user_id).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Remove photo file if exists
    if db_user.reference_photo_path and os.path.exists(db_user.reference_photo_path):
        os.remove(db_user.reference_photo_path)
    
    # Remove from face recognition service
    face_recognition_service.remove_known_face(user_id)
    
    # Delete from database
    db.delete(db_user)
    db.commit()
    
    return {"message": "User deleted successfully"}

@router.post("/reload-faces")
def reload_faces(db: Session = Depends(get_db)):
    """Reload all face encodings into the face recognition service."""
    users = db.query(User).filter(User.is_active == True, User.face_encoding.isnot(None)).all()
    
    users_data = []
    for user in users:
        users_data.append({
            'user_id': user.user_id,
            'face_encoding': user.face_encoding
        })
    
    face_recognition_service.load_known_faces(users_data)
    
    return {"message": f"Reloaded {len(users_data)} face encodings"}
